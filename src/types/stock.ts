// User Holdings API Response Types
export interface UserHolding {
  id: string;
  stock_code: string;
  quantity: number;
  value: number; // Gi<PERSON> mua tính theo nghìn <PERSON> (cần nhân 1000 để có giá thực tế)
  created_at: string;
  updated_at: string;
  user: string;
}

// WebSocket Data Types
export interface StockRealtimeData {
  e: string; // event type
  ex: string; // exchange
  t: string; // timestamp
  s: string; // symbol
  r: number; // reference price
  ce: number; // ceiling price
  f: number; // floor price
  p: number; // current price
  h: number; // high price
  l: number; // low price
  o: number; // open price
  c: number; // close price
  a: number; // average price
  tv: number; // total volume
  tt: number;
  v: number; // volume
  tva: number; // total value
  bc: number;
  sc: number;
  bq: number;
  sq: number;
  bfq: number;
  sfq: number;
  bfv: number;
  sfv: number;
  fr: number;
  tbv: number;
  ssv: number;
  pc: number;
  pn: number;
  // Bid prices and quantities
  pb1: number;
  qb1: number;
  pb2: number;
  qb2: number;
  pb3: number;
  qb3: number;
  // Ask prices and quantities
  pa1: number;
  qa1: number;
  pa2: number;
  qa2: number;
  pa3: number;
  qa3: number;
}

export interface WebSocketMessage {
  topic: string;
  data: StockRealtimeData[];
  list?: boolean;
}

export interface WebSocketSubscription {
  event: string;
  topic: string;
  params: string[];
}

// Combined data for UI display
export interface StockHoldingWithRealtimeData {
  id: string;
  stockCode: string;
  quantity: number;
  buyPrice: number; // Giá mua thực tế (VNĐ)
  currentPrice: number; // Giá hiện tại (VNĐ)
  totalValue: number; // quantity * buyPrice (VNĐ)
  currentValue: number; // quantity * currentPrice (VNĐ)
  profitLoss: number; // currentValue - totalValue (VNĐ)
  profitLossPercentage: number; // (profitLoss / totalValue) * 100
  realtimeData?: StockRealtimeData;
}

export interface PortfolioSummary {
  totalInvestment: number; // Sum of all holdings' totalValue
  currentValue: number; // Sum of all holdings' currentValue
  totalProfitLoss: number; // Sum of all holdings' profitLoss
  profitLossPercentage: number; // (totalProfitLoss / totalInvestment) * 100
}
