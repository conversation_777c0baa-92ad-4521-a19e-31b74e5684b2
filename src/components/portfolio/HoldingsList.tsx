import React from 'react';
import { TrendingUp, TrendingDown, Activity, RefreshCw } from 'lucide-react';
import { StockHoldingWithRealtimeData } from '../../types/stock';

interface HoldingsListProps {
  holdings: StockHoldingWithRealtimeData[];
  loading?: boolean;
  onRefresh?: () => void;
}

export const HoldingsList: React.FC<HoldingsListProps> = ({ holdings, loading, onRefresh }) => {
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('vi-VN', {
      style: 'currency',
      currency: 'VND',
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatNumber = (num: number) => {
    return new Intl.NumberFormat('vi-VN').format(num);
  };

  const formatPercentage = (percentage: number) => {
    const sign = percentage >= 0 ? '+' : '';
    return `${sign}${percentage.toFixed(2)}%`;
  };

  if (loading) {
    return (
      <div className="bg-slate-900/50 backdrop-blur-sm border border-slate-700 rounded-xl p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-white">Danh Mục Đầu Tư</h3>
          <Activity className="w-5 h-5 text-slate-400" />
        </div>
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="animate-pulse p-4 bg-slate-800/50 rounded-lg">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-slate-700 rounded-full"></div>
                  <div>
                    <div className="w-16 h-4 bg-slate-700 rounded mb-2"></div>
                    <div className="w-24 h-3 bg-slate-700 rounded"></div>
                  </div>
                </div>
                <div className="text-right">
                  <div className="w-20 h-4 bg-slate-700 rounded mb-2"></div>
                  <div className="w-16 h-3 bg-slate-700 rounded"></div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (holdings.length === 0) {
    return (
      <div className="bg-slate-900/50 backdrop-blur-sm border border-slate-700 rounded-xl p-6">
        <div className="flex items-center justify-between mb-6">
          <h3 className="text-lg font-semibold text-white">Danh Mục Đầu Tư</h3>
          <div className="flex items-center gap-2">
            {onRefresh && (
              <button
                type="button"
                onClick={onRefresh}
                className="p-2 text-slate-400 hover:text-white transition-colors"
                title="Refresh"
              >
                <RefreshCw className="w-4 h-4" />
              </button>
            )}
            <Activity className="w-5 h-5 text-slate-400" />
          </div>
        </div>
        <div className="text-center py-8">
          <Activity className="w-12 h-12 text-slate-600 mx-auto mb-4" />
          <p className="text-slate-400">Chưa có cổ phiếu nào trong danh mục</p>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-slate-900/50 backdrop-blur-sm border border-slate-700 rounded-xl p-6">
      <div className="flex items-center justify-between mb-6">
        <h3 className="text-lg font-semibold text-white">Danh Mục Đầu Tư</h3>
        <div className="flex items-center gap-2">
          {onRefresh && (
            <button
              type="button"
              onClick={onRefresh}
              className="p-2 text-slate-400 hover:text-white transition-colors"
              title="Refresh"
            >
              <RefreshCw className="w-4 h-4" />
            </button>
          )}
          <Activity className="w-5 h-5 text-slate-400" />
        </div>
      </div>
      
      <div className="space-y-4">
        {holdings.map((holding) => {
          const isPositive = holding.profitLoss >= 0;
          const hasRealtimeData = !!holding.realtimeData;
          
          return (
            <div 
              key={holding.id} 
              className="p-4 bg-slate-800/50 rounded-lg hover:bg-slate-800/70 transition-all duration-200 border border-slate-700/50"
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4">
                  <div className="w-12 h-12 bg-gradient-to-br from-emerald-600 to-blue-600 rounded-full flex items-center justify-center">
                    <span className="text-white font-bold text-sm">{holding.stockCode}</span>
                  </div>
                  <div>
                    <div className="flex items-center gap-2">
                      <h4 className="text-white font-semibold">{holding.stockCode}</h4>
                      {hasRealtimeData && (
                        <div className="w-2 h-2 bg-emerald-400 rounded-full animate-pulse"></div>
                      )}
                    </div>
                    <p className="text-slate-400 text-sm">
                      {formatNumber(holding.quantity)} cổ phiếu • Giá mua: {formatCurrency(holding.buyPrice)}
                    </p>
                  </div>
                </div>
                
                <div className="text-right">
                  <div className="text-white font-semibold text-lg">
                    {formatCurrency(holding.currentPrice)}
                  </div>
                  <div className={`flex items-center gap-1 text-sm ${isPositive ? 'text-emerald-400' : 'text-red-400'}`}>
                    {isPositive ? (
                      <TrendingUp className="w-4 h-4" />
                    ) : (
                      <TrendingDown className="w-4 h-4" />
                    )}
                    <span>{formatCurrency(holding.profitLoss)}</span>
                    <span>({formatPercentage(holding.profitLossPercentage)})</span>
                  </div>
                </div>
              </div>
              
              <div className="mt-3 pt-3 border-t border-slate-700/50">
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    <span className="text-slate-400">Giá trị đầu tư:</span>
                    <span className="text-white ml-2">{formatCurrency(holding.totalValue)}</span>
                  </div>
                  <div>
                    <span className="text-slate-400">Giá trị hiện tại:</span>
                    <span className="text-white ml-2">{formatCurrency(holding.currentValue)}</span>
                  </div>
                </div>
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};
