import { useState, useEffect, useCallback } from 'react';
import { StockService } from '../services/stockService';
import { WebSocketService } from '../services/websocketService';
import { 
  UserHolding, 
  StockRealtimeData, 
  StockHoldingWithRealtimeData,
  PortfolioSummary
} from '../types/stock';

export const useStockPortfolio = () => {
  const [holdings, setHoldings] = useState<UserHolding[]>([]);
  const [realtimeData, setRealtimeData] = useState<Record<string, StockRealtimeData>>({});
  const [combinedData, setCombinedData] = useState<StockHoldingWithRealtimeData[]>([]);
  const [portfolioSummary, setPortfolioSummary] = useState<PortfolioSummary>({
    totalInvestment: 0,
    currentValue: 0,
    totalProfitLoss: 0,
    profitLossPercentage: 0
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const stockService = StockService.getInstance();
  const websocketService = WebSocketService.getInstance();

  // Fetch user holdings
  const fetchHoldings = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await stockService.getUserHoldings();
      setHoldings(data);
      return data;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch holdings');
      return [];
    } finally {
      setLoading(false);
    }
  }, []);

  // Handle realtime data updates
  const handleRealtimeUpdate = useCallback((stockCode: string, data: StockRealtimeData) => {
    console.log(`Realtime update for ${stockCode}:`, data);
    setRealtimeData(prev => {
      const updated = {
        ...prev,
        [stockCode]: data
      };
      console.log('Updated realtime data:', updated);
      return updated;
    });
  }, []);

  // Subscribe to WebSocket for each holding
  const subscribeToHoldings = useCallback((holdingsData: UserHolding[]) => {
    console.log('Subscribing to holdings:', holdingsData.map(h => h.stock_code));

    if (!websocketService.isConnected()) {
      console.log('WebSocket not connected, connecting...');
      websocketService.connect().then(() => {
        console.log('WebSocket connected, subscribing to stocks...');
        holdingsData.forEach(holding => {
          console.log(`Subscribing to ${holding.stock_code}`);
          websocketService.subscribeToStock(
            holding.stock_code,
            (data) => handleRealtimeUpdate(holding.stock_code, data)
          );
        });
      }).catch(err => {
        setError('Failed to connect to WebSocket');
        console.error('WebSocket connection error:', err);
      });
    } else {
      console.log('WebSocket already connected, subscribing to stocks...');
      holdingsData.forEach(holding => {
        console.log(`Subscribing to ${holding.stock_code}`);
        websocketService.subscribeToStock(
          holding.stock_code,
          (data) => handleRealtimeUpdate(holding.stock_code, data)
        );
      });
    }
  }, [handleRealtimeUpdate]);

  // Combine holdings with realtime data
  const combineData = useCallback(() => {
    if (holdings.length === 0) return;

    const combined = holdings.map(holding => {
      const stockData = realtimeData[holding.stock_code];
      // Convert value from thousands VND to actual VND (value * 1000)
      const buyPrice = holding.value * 1000;

      // Only use realtime price if we have valid data, otherwise use buyPrice
      // This prevents showing 0 profit/loss when there's no realtime data
      let currentPrice = buyPrice; // Default to buy price
      let profitLoss = 0;
      let profitLossPercentage = 0;

      if (stockData?.p && stockData.p > 0) {
        currentPrice = stockData.p;
        const totalValue = holding.quantity * buyPrice;
        const currentValue = holding.quantity * currentPrice;
        profitLoss = currentValue - totalValue;
        profitLossPercentage = totalValue > 0 ? (profitLoss / totalValue) * 100 : 0;

        console.log(`${holding.stock_code}: Buy=${buyPrice}, Current=${currentPrice}, P/L=${profitLoss}`);
      } else {
        console.log(`${holding.stock_code}: No valid realtime data, using buy price`);
      }

      const totalValue = holding.quantity * buyPrice;
      const currentValue = holding.quantity * currentPrice;

      return {
        id: holding.id,
        stockCode: holding.stock_code,
        quantity: holding.quantity,
        buyPrice,
        currentPrice,
        totalValue,
        currentValue,
        profitLoss,
        profitLossPercentage,
        realtimeData: stockData
      };
    });

    setCombinedData(combined);

    // Calculate portfolio summary
    const totalInvestment = combined.reduce((sum, item) => sum + item.totalValue, 0);
    const currentValue = combined.reduce((sum, item) => sum + item.currentValue, 0);
    const totalProfitLoss = currentValue - totalInvestment;
    const profitLossPercentage = totalInvestment > 0 ? (totalProfitLoss / totalInvestment) * 100 : 0;

    console.log('Portfolio Summary:', {
      totalInvestment,
      currentValue,
      totalProfitLoss,
      profitLossPercentage
    });

    setPortfolioSummary({
      totalInvestment,
      currentValue,
      totalProfitLoss,
      profitLossPercentage
    });
  }, [holdings, realtimeData]);

  // Initialize data on component mount
  useEffect(() => {
    const init = async () => {
      const holdingsData = await fetchHoldings();
      if (holdingsData.length > 0) {
        subscribeToHoldings(holdingsData);
      }
    };

    init();

    return () => {
      // Cleanup WebSocket connection on unmount
      websocketService.disconnect();
    };
  }, []); // Empty dependency array to run only once on mount

  // Update combined data whenever holdings or realtime data changes
  useEffect(() => {
    combineData();
  }, [holdings, realtimeData]); // Remove combineData from dependencies to prevent infinite loop

  return {
    holdings: combinedData,
    rawHoldings: holdings,
    portfolioSummary,
    loading,
    error,
    refreshHoldings: fetchHoldings
  };
};
